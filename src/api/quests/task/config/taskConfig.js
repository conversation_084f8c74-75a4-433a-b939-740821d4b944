import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/taskConfig',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/taskConfig/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/taskConfig',
    method: 'put',
    data
  })
}

export function getTaskConfigDetail(id) {
  return request({
    url: 'api/taskConfig/detail/' + id,
    method: 'get'
  })
}

export function releasePublish(data) {
  return request({
    url: 'api/taskConfig/releasePublish/',
    method: 'post',
    data
  })
}

export function greyPublish(data) {
  return request({
    url: 'api/taskConfig/greyPublish/',
    method: 'post',
    data
  })
}

export function timingPublish(data) {
  return request({
    url: 'api/taskConfig/timingPublish/',
    method: 'post',
    data
  })
}

export function exportJson(id) {
  return request({
    url: 'api/taskConfig/exportJson/' + id,
    method: 'get'
  })
}

export function exportAllJson(ids) {
  return request({
    url: 'api/taskConfig/exportAllJson/' + ids,
    method: 'get'
  })
}

export function importJSON(data) {
  return request({
    url: 'api/taskConfig/importJson',
    method: 'post',
    data
  })
}

export default { add, edit, del, releasePublish, greyPublish, exportJson, importJSON, timingPublish, exportAllJson }
