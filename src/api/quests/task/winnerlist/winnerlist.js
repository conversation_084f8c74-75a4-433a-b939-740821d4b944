import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/winnerlist',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: 'api/winnerlist/delete',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: 'api/winnerlist',
    method: 'put',
    data
  })
}

export function queryList(params) {
  return request({
    url: 'api/winnerlist',
    method: 'get',
    params
  })
}

export default { add, edit, del, queryList }
