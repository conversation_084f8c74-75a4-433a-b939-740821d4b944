import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/notice',
    method: 'post',
    data
  })
}

export function deleteNotice(ids) {
  return request({
    url: 'api/notice/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/notice',
    method: 'put',
    data
  })
}

export function publish(id) {
  return request({
    url: 'api/notice/publish',
    method: 'post',
    data: id
  })
}

export default { add, edit, deleteNotice, publish }
