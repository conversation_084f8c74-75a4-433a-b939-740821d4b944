import request from '@/utils/request'
import { download } from '@/utils/download'

export function add(data) {
  return request({
    url: 'api/innerTest',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/innerTest/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/innerTest',
    method: 'put',
    data
  })
}

export function downloadInnerTestUsers() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}`
  const fileName = `innerTestUser${timestamp}.csv`

  return download('api/innerTest/download', {}, fileName)
}

/**
 * 上传内测用户文件
 * @param {FormData} formData 包含文件的 FormData 对象
 * @returns {Promise}
 */
export function uploadInnerTestUsers(formData) {
  return request({
    url: 'api/innerTest/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export default { add, edit, del, downloadInnerTestUsers, uploadInnerTestUsers }
