import request from '@/utils/request'

// 查询钱包绑定关系
export function queryWalletBinding(data) {
  return request({
    url: 'api/wallet/binding/query',
    method: 'post',
    data
  })
}

// 解绑钱包
export function unbindWallet(data) {
  return request({
    url: 'api/wallet/unbind',
    method: 'post',
    data
  })
}

// 查询社媒绑定关系
export function querySocialBinding(data) {
  return request({
    url: 'api/social/binding/query',
    method: 'post',
    data
  })
}

// 解绑社媒
export function unbindSocial(data) {
  return request({
    url: 'api/social/unbind',
    method: 'post',
    data
  })
}

export default { queryWalletBinding, unbindWallet, querySocialBinding, unbindSocial }
