import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/information',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/information',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/information',
    method: 'put',
    data
  })
}

export function publish(id) {
  return request({
    url: 'api/information/publish',
    method: 'post',
    data: id
  })
}

export function withdraw(id) {
  return request({
    url: 'api/information/withdraw',
    method: 'post',
    data: id
  })
}

export default { add, edit, del, publish, withdraw }
