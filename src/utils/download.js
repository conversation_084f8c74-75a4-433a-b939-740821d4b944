import axios from 'axios'
import { getToken } from '@/utils/auth'
import { baseUrl } from '@/utils/request'

/**
 * 下载文件
 * @param {String} url 请求地址
 * @param {Object} params 请求参数
 * @param {String} filename 文件名
 */
export function download(url, params, filename) {
  return axios.create({
    baseURL: baseUrl,
    timeout: 120000,
    responseType: 'blob',
    headers: {
      'Authorization': getToken()
    }
  }).get(url, { params }).then(response => {
    const blob = new Blob([response.data])
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.click()
    URL.revokeObjectURL(link.href)
  }).catch(error => {
    console.error('Download error', error)
  })
}
