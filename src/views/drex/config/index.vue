<template>
  <div class="app-container">

    <!-- 表单区域 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>刷新运营参数</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" inline label-width="80px">
        <el-row :gutter="20" type="flex" align="middle">
          <el-col :span="6">
            <el-form-item label="所属组件" prop="projectComponent" disabled>
              <el-select v-model="form.projectComponent" filterable placeholder="选择组件" style="width: 80%" disabled>
                <el-option
                  v-for="item in dict.project_component"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="参数名称" prop="paramterKey">
              <el-input v-model="form.paramterKey" placeholder="请输入参数名称" style="width: 120%" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="参数值" prop="paramterValue">
              <el-input v-model="form.paramterValue" placeholder="请输入参数值" style="width: 120%" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" @click="submitForm">提交</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜索区域 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>运营参数列表</span>
      </div>
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="所属组件" prop="projectComponent">
          <el-select v-model="searchForm.projectComponent" filterable placeholder="选择组件">
            <el-option
              v-for="item in dict.project_component"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参数名称" prop="paramterKey">
          <el-input v-model="searchForm.paramterKey" placeholder="请输入参数名称" style="width: 100%" />
        </el-form-item>
        <el-form-item>
          <el-button type="button" class="el-button filter-item el-button--success el-button--mini" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="所属组件" prop="projectComponent" align="center" />
        <el-table-column label="参数名称" prop="paramterKey" align="center" />
        <el-table-column label="参数值" prop="paramterValue" align="center" />
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="{ row }">
            <el-button type="primary" size="mini" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

    </el-card>

  </div>
</template>

<script>
import { refresh, queryList } from '@/api/drex/config/operateConfig'

export default {
  dicts: ['project_component'],
  data() {
    return {
      searchForm: {
        projectComponent: '',
        paramterKey: ''
      },
      form: {
        projectComponent: '',
        paramterKey: '',
        paramterValue: ''
      },
      rules: {
        projectComponent: [
          { required: true, message: '请输入所属组件', trigger: 'blur' }
        ],
        paramterKey: [
          { required: true, message: '请输入参数名称', trigger: 'blur' }
        ],
        paramterValue: [
          { required: true, message: '请输入参数值', trigger: 'blur' }
        ]
      },
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList() {
      const params = {
        ...this.listQuery,
        ...this.searchForm
      }
      queryList(params).then(response => {
        console.log(response)
        if (response && response.length > 0) {
          this.list = response.map(item => ({
            projectComponent: item.projectComponent,
            paramterKey: item.paramterKey,
            paramterValue: item.paramterValue
          }))
          this.total = response.length
          this.$message({
            message: '数据加载成功',
            type: 'success'
          })
        } else {
          this.list = []
          this.total = 0
          this.$message({
            message: '暂无数据',
            type: 'info'
          })
        }
      }).catch(error => {
        this.list = []
        this.total = 0
        this.$message.error(error.message || '数据加载失败')
      })
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          refresh(this.form).then(response => {
            const success = response
            if (success) {
              this.$message({
                message: '操作成功',
                type: 'success'
              })
              this.resetForm()
              this.getList()
            } else {
              this.$message({
                message: '操作失败',
                type: 'error'
              })
            }
          }).catch(error => {
            this.$message.error(error.message)
          })
        }
      })
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
    },
    // 处理搜索
    handleSearch() {
      this.listQuery.page = 1
      this.getList()
    },
    // 重置搜索
    handleResetSearch() {
      this.$refs.searchForm.resetFields()
      this.getList()
    },
    // 编辑
    handleEdit(row) {
      this.form = Object.assign({}, row)
    },

    // 分页处理
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    }
  }
}

</script>

<style scoped>
.app-container {
  padding: 20px;
}
.box-card {
  margin-bottom: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
