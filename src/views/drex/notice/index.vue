<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">消息id</label>
        <el-input v-model="query.id" clearable placeholder="消息id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">标题</label>
        <el-input v-model="query.title" clearable placeholder="标题" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">通知状态</label>
        <el-select v-model="query.status" clearable size="small" placeholder="通知状态" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.notify_status" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">目标用户</label>
        <el-select v-model="query.sendTo" clearable size="small" placeholder="目标用户" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.customer_group" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <rrOperation :crud="crud" />

        <el-button type="primary" class="filter-item el-button--mini" style="margin-left: 10%" @click="downloadInnerTestUsers">
          <i class="el-icon-download" /><span>导出内测用户</span>
        </el-button>
        <div style="display: inline-block">
          <input
            ref="fileInput"
            type="file"
            accept=".csv"
            style="display: none"
            @change="handleFileChange"
          >
          <el-button
            class="el-button filter-item el-button--primary el-button--mini"
            @click="confirmBeforeUpload"
          >
            <i class="el-icon-upload2" /><span>导入内测用户</span>
          </el-button>
        </div>

      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />

      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="标题" prop="title">
            <el-tooltip v-if="form.title && form.title.length > 50" :content="form.title" placement="top">
              <el-input v-model="form.title" :rows="3" type="textarea" style="width: 100%" />
            </el-tooltip>
            <el-input v-else v-model="form.title" :rows="3" type="textarea" style="width: 100%" />
          </el-form-item>
          <el-form-item label="副标题">
            <el-tooltip v-if="form.subTitle && form.subTitle.length > 50" :content="form.subTitle" placement="top">
              <el-input v-model="form.subTitle" :rows="3" type="textarea" style="width: 100%" />
            </el-tooltip>
            <el-input v-else v-model="form.subTitle" :rows="3" type="textarea" style="width: 100%" />
          </el-form-item>
          <el-form-item label="通知内容">
            <el-tooltip v-if="form.content && form.content.length > 50" :content="form.content" placement="top">
              <el-input v-model="form.content" :rows="3" type="textarea" style="width: 100%" />
            </el-tooltip>
            <el-input v-else v-model="form.content" :rows="3" type="textarea" style="width: 100%" />
          </el-form-item>
          <el-form-item label="跳转链接">
            <el-tooltip v-if="form.link && form.link.length > 50" :content="form.link" placement="top">
              <el-input v-model="form.link" :rows="3" type="textarea" style="width: 100%" />
            </el-tooltip>
            <el-input v-else v-model="form.link" :rows="3" type="textarea" style="width: 100%" />
          </el-form-item>
          <el-form-item label="通知时间">
            <el-date-picker
              v-model="form.notifyTime"
              type="datetime"
              style="width: 370px;"
              :picker-options="pickerOptions"
              placeholder="选择通知时间（不能选择过去的时间）"
            />
          </el-form-item>
          <el-form-item label="目标用户">
            <el-select v-model="form.sendTo" filterable placeholder="请选择">
              <el-option
                v-for="item in dict.customer_group"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="消息id" />
        <el-table-column prop="title" label="标题">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.title && scope.row.title.length > 50" :content="scope.row.title" placement="top">
              <span>{{ truncateText(scope.row.title, 50) }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="subTitle" label="副标题">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.subTitle && scope.row.subTitle.length > 50" :content="scope.row.subTitle" placement="top">
              <span>{{ truncateText(scope.row.subTitle, 50) }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.subTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="通知内容">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.content && scope.row.content.length > 50" :content="scope.row.content" placement="top">
              <span>{{ truncateText(scope.row.content, 50) }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.content }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="link" label="跳转链接">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.link && scope.row.link.length > 50" :content="scope.row.link" placement="top">
              <span>{{ truncateText(scope.row.link, 50) }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.link }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="notifyTime" label="通知时间">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.notifyTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="通知状态">
          <template slot-scope="scope">
            {{ dict.label.notify_status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="sendTo" label="目标用户">
          <template slot-scope="scope">
            {{ dict.label.customer_group[scope.row.sendTo] }}
          </template>
        </el-table-column>
        <!--        <el-table-column v-if="checkPer(['admin','notice:edit','notice:del'])" label="编辑/删除" width="150px" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            <udOperation-->
        <!--              :data="scope.row"-->
        <!--              :permission="permission"-->
        <!--            />-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="编辑操作" align="center">
          <template slot-scope="scope">
            <el-button type="success" size="mini" :disabled="scope.row.status !== 'Pending'" @click="editNotice(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column label="删除操作" align="center">
          <template slot-scope="scope">
            <el-button type="danger" size="mini" :disabled="scope.row.status !== 'Pending'" @click="delNotice(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column label="发布操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" :disabled="scope.row.status !== 'Pending'" @click="publishNotice(scope.row)">发布</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudNotice, { deleteNotice } from '@/api/drex/notice/notice'
import { downloadInnerTestUsers, uploadInnerTestUsers } from '@/api/drex/innerTest/innerTest'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { publish } from '@/api/drex/notice/notice'

const defaultForm = { id: null, title: null, subTitle: null, content: null, link: null, notifyTime: null, status: null, sendTo: null }
export default {
  name: 'Notice',
  components: { pagination, crudOperation, rrOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['notify_status', 'customer_group'],
  cruds() {
    return CRUD({ title: '通知消息配置', url: 'api/notice', idField: 'id', sort: 'id,desc', crudMethod: { ...crudNotice }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'notice:add'],
        edit: ['admin', 'notice:edit'],
        del: ['admin', 'notice:del']
      },
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() < Date.now()
        },
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: '明天',
          onClick(picker) {
            const date = new Date()
            date.setTime(date.getTime() + 3600 * 1000 * 24)
            picker.$emit('pick', date)
          }
        }, {
          text: '一周后',
          onClick(picker) {
            const date = new Date()
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
            picker.$emit('pick', date)
          }
        }]
      },
      rules: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        notifyTime: [
          { required: true, message: '通知时间不能为空', trigger: 'change' }
        ]
      },
      queryTypeOptions: [
        { key: 'id', display_name: '消息id' },
        { key: 'title', display_name: '标题' },
        { key: 'status', display_name: '通知状态' },
        { key: 'sendTo', display_name: '目标用户' }
      ]
    }
  },
  // No computed properties needed
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    // 钩子：表单提交前执行
    [CRUD.HOOK.beforeSubmit]() {
      if (this.form.notifyTime && this.form.notifyTime.getTime() < Date.now()) {
        this.$message.error('不能选择小于当前时间的时间点')
        return false
      }
      return true
    },
    publishNotice(row) {
      this.$confirm(`确定发布通知消息 ${row.title} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        publish(row.id).then(result => {
          this.crud.notify(result, 'success')
          this.crud.resetQuery()
        })
      })
    },
    editNotice(row) {
      this.crud.toEdit(row)
    },
    delNotice(id) {
      this.$confirm(`确定删除通知消息 ${id} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteNotice([id]).then(result => {
          this.crud.notify(result, 'success')
          this.crud.resetQuery()
        })
      })
    },
    addNotice() {
      this.crud.toAdd()
    },

    // 上传前确认
    confirmBeforeUpload() {
      this.$confirm('请确保您导出了最新的内测用户数据，并基于原始数据更新后上传原始文件', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 点击确认后触发文件选择器
        this.$refs.fileInput.click()
      }).catch(() => {
        // 点击取消后不做任何操作
        this.$message({
          type: 'info',
          message: '已取消上传'
        })
      })
    },
    // 处理文件选择变化
    handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return

      // 校验文件格式和大小
      if (file.name.split('.').pop().toLowerCase() !== 'csv') {
        this.$message.error('上传文件格式必须是csv!')
        this.$refs.fileInput.value = '' // 清空文件输入
        return
      }

      if (file.size / 1024 / 1024 > 500) {
        this.$message.error('上传文件大小不能超过 500MB!')
        this.$refs.fileInput.value = '' // 清空文件输入
        return
      }

      // 使用我们创建的方法上传文件
      this.uploadInnerTestUsers(file)

      // 清空文件输入，以便下次选择同一文件时也能触发变化事件
      this.$refs.fileInput.value = ''
    },
    // 导出内测用户
    downloadInnerTestUsers() {
      this.$message({
        message: '正在导出内测用户数据，请稍后...',
        type: 'info'
      })
      downloadInnerTestUsers().then(() => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
      }).catch(error => {
        this.$message({
          message: '导出失败: ' + error,
          type: 'error'
        })
      })
    },
    // 程序化触发内测用户上传
    uploadInnerTestUsers(file) {
      const formData = new FormData()
      formData.append('file', file)

      this.$message({
        message: '正在上传内测用户数据，请稍后...',
        type: 'info'
      })

      uploadInnerTestUsers(formData).then(response => {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
      }).catch(error => {
        this.$message({
          message: '上传失败: ' + error,
          type: 'error'
        })
      })
    },
    // 截断文本，如果超过指定长度则添加省略号
    truncateText(text, maxLength) {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    },
    // 格式化时间戳为 UTC+8 时间格式
    formatDateTime(timestamp) {
      if (!timestamp) return '-' // 处理空值
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // 24小时制
      }).replace(/\//g, '-') // 将 / 替换为 -
    }
  }
}
</script>

<style scoped>

</style>
