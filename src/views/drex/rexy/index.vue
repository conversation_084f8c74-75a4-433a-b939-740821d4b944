<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" style="width: 370px;" aria-required="true" />
          </el-form-item>
          <el-form-item label="等级" prop="level">
            <el-select v-model="form.level" filterable placeholder="请选择">
              <el-option
                v-for="item in dict.kol"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="生产速率" prop="rate">
            <el-input v-model="form.rate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="篮子上限" prop="limit">
            <el-input v-model="form.limit" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否为默认" prop="isDefault">
            <el-select v-model="form.isDefault" filterable placeholder="请选择">
              <el-option
                v-for="item in dict.boolean"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="头像" prop="circleAvatar">
            <div style="display: flex; align-items: center">
              <el-input
                v-model="form.circleAvatar"
                placeholder="只能上传jpg/png文件，且不超过5M"
                style="width: 85%; margin-right: 5px"
              />
              <el-upload
                ref="rexyLogo1Upload"
                style="display: inline-block"
                :limit="1"
                :before-upload="beforeUpload"
                :action="rexyImageUploadApi"
                :show-file-list="false"
                :headers="headers"
                :on-success="handleSuccessRexy1Logo"
                :on-error="handleError"
              >
                <el-button size="small" type="success">点击上传</el-button>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="列表小图" prop="miniAvatar">
            <div style="display: flex; align-items: center">
              <el-input
                v-model="form.miniAvatar"
                placeholder="只能上传jpg/png文件，且不超过5M"
                style="width: 85%; margin-right: 5px"
              />
              <el-upload
                ref="rexyLogo2Upload"
                style="display: inline-block"
                :limit="1"
                :before-upload="beforeUpload"
                :action="rexyImageUploadApi"
                :show-file-list="false"
                :headers="headers"
                :on-success="handleSuccessRexy2Logo"
                :on-error="handleError"
              >
                <el-button size="small" type="success">点击上传</el-button>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="列表大图" prop="avatar">
            <div style="display: flex; align-items: center">
              <el-input
                v-model="form.avatar"
                placeholder="只能上传jpg/png文件，且不超过5M"
                style="width: 85%; margin-right: 5px"
              />
              <el-upload
                ref="rexyLogo3Upload"
                style="display: inline-block"
                :limit="1"
                :before-upload="beforeUpload"
                :action="rexyImageUploadApi"
                :show-file-list="false"
                :headers="headers"
                :on-success="handleSuccessRexy3Logo"
                :on-error="handleError"
              >
                <el-button size="small" type="success">点击上传</el-button>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="生效时间" prop="effectiveTime">
            <el-date-picker v-model="form.effectiveTime" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="过期时间" prop="expirationTime">
            <el-date-picker v-model="form.expirationTime" type="datetime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="level" label="等级">
          <template slot-scope="scope">
            {{ dict.label.kol[scope.row.isDefault] }}
          </template>
        </el-table-column>
        <el-table-column prop="rate" label="生产速率" />
        <el-table-column prop="limit" label="篮子上限" />
        <el-table-column prop="isDefault" label="是否为默认">
          <template slot-scope="scope">
            {{ dict.label.boolean[scope.row.isDefault] }}
          </template>
        </el-table-column>
        <el-table-column prop="avatar" label="头像" />
        <el-table-column prop="effectiveTime" label="生效时间" />
        <el-table-column prop="expirationTime" label="过期时间" />
        <el-table-column prop="miniAvatar" label="列表小图" />
        <el-table-column prop="circleAvatar" label="头像" />
        <el-table-column v-if="checkPer(['admin','rexyConfig:edit','rexyConfig:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudRexyConfig from '@/api/drex/rexy/rexyConfig'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { getToken } from '@/utils/auth'

const defaultForm = { id: null, name: null, level: null, rate: null, limit: null, isDefault: null, avatar: null, effectiveTime: null, expirationTime: null, miniAvatar: null, circleAvatar: null }
export default {
  name: 'RexyConfig',
  components: { pagination, crudOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['boolean', 'kol'],
  cruds() {
    return CRUD({ title: 'RexyConfigController', url: 'api/rexyConfig', idField: 'id', sort: 'id,desc', crudMethod: { ...crudRexyConfig }})
  },
  data() {
    return {
      headers: {
        'Authorization': getToken()
      },
      permission: {
        add: ['admin', 'rexyConfig:add'],
        edit: ['admin', 'rexyConfig:edit'],
        del: ['admin', 'rexyConfig:del']
      },
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '等级不能为空', trigger: 'blur' }
        ],
        rate: [
          { required: true, message: '生产速率不能为空', trigger: 'blur' }
        ],
        limit: [
          { required: true, message: '篮子上限不能为空', trigger: 'blur' }
        ],
        isDefault: [
          { required: true, message: '是否为默认不能为空', trigger: 'blur' }
        ],
        avatar: [
          { required: true, message: '头像不能为空', trigger: 'blur' }
        ],
        miniAvatar: [
          { required: true, message: '列表小图不能为空', trigger: 'blur' }
        ],
        circleAvatar: [
          { required: true, message: '头像不能为空', trigger: 'blur' }
        ]
      }}
  },
  computed: {
    ...mapGetters([
      'rexyImageUploadApi'
    ])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    beforeUpload(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 5MB!')
      }
      return isLt2M
    },
    handleSuccessRexy1Logo(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.circleAvatar = response
      // 重置上传组件，以便下次上传
      this.$refs.rexyLogo1Upload.clearFiles()
    },
    handleSuccessRexy2Logo(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.miniAvatar = response
      // 重置上传组件，以便下次上传
      this.$refs.rexyLogo2Upload.clearFiles()
    },
    handleSuccessRexy3Logo(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.avatar = response
      // 重置上传组件，以便下次上传
      this.$refs.rexyLogo3Upload.clearFiles()
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    }
  }
}
</script>

<style scoped>

</style>
