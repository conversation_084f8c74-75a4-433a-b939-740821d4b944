<template>
  <div class="app-container">
    <!-- 功能选择标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 钱包解绑 -->
      <el-tab-pane label="钱包解绑" name="wallet">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>钱包解绑管理</span>
          </div>

          <!-- 钱包查询表单 -->
          <el-form ref="walletQueryForm" :model="walletQueryForm" :rules="walletQueryRules" label-width="120px" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="HandleName" prop="handleName">
                  <el-input
                    v-model="walletQueryForm.handleName"
                    placeholder="请输入用户handleName"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="WalletAddress" prop="walletAddress">
                  <el-input
                    v-model="walletQueryForm.walletAddress"
                    placeholder="请输入钱包地址"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item>
                  <el-button type="primary" :loading="walletQueryLoading" @click="handleWalletQuery">
                    <i class="el-icon-search" /> 查询绑定关系
                  </el-button>
                  <el-button @click="handleWalletReset">
                    <i class="el-icon-refresh" /> 重置
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- 钱包绑定关系展示 -->
          <div v-if="walletBindingInfo" class="binding-info">
            <el-divider content-position="left">钱包绑定关系信息</el-divider>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="钱包类型">{{ walletBindingInfo.walletType }}</el-descriptions-item>
              <el-descriptions-item label="钱包地址">{{ walletBindingInfo.walletAddress }}</el-descriptions-item>
              <el-descriptions-item label="绑定类型">
                <el-tag :type="walletBindingInfo.connectType === 'KEY' ? 'danger' : 'success'">
                  {{ walletBindingInfo.connectType === 'KEY' ? 'KEY绑定' : 'BIND绑定' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="绑定时间">{{ walletBindingInfo.connectedAt }}</el-descriptions-item>
            </el-descriptions>

            <!-- 钱包解绑操作 -->
            <div class="unbind-actions">
              <el-button
                v-if="walletBindingInfo && walletBindingInfo.connectType !== 'KEY'"
                type="danger"
                :loading="walletUnbindLoading"
                :disabled="!walletBindingInfo"
                @click="handleWalletUnbind"
              >
                <i class="el-icon-delete" /> 解绑钱包
              </el-button>
            </div>
          </div>

          <!-- 无钱包绑定关系提示 -->
          <div v-else-if="walletQueryExecuted && !walletBindingInfo" class="no-binding">
            <el-empty description="未找到钱包绑定关系">
              <el-button type="primary" @click="handleWalletReset">重新查询</el-button>
            </el-empty>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 社媒解绑 -->
      <el-tab-pane v-if="false" label="社媒解绑" name="social">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>社媒解绑管理</span>
          </div>

          <!-- 社媒查询表单 -->
          <el-form ref="socialQueryForm" :model="socialQueryForm" :rules="socialQueryRules" label-width="120px" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="HandleName" prop="handleName">
                  <el-input
                    v-model="socialQueryForm.handleName"
                    placeholder="请输入用户handleName"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Platform" prop="platform">
                  <el-select
                    v-model="socialQueryForm.platform"
                    placeholder="请选择平台"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in platformOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item>
                  <el-button type="primary" :loading="socialQueryLoading" @click="handleSocialQuery">
                    <i class="el-icon-search" /> 查询绑定关系
                  </el-button>
                  <el-button @click="handleSocialReset">
                    <i class="el-icon-refresh" /> 重置
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- 社媒绑定关系展示 -->
          <div v-if="socialBindingInfo" class="binding-info">
            <el-divider content-position="left">社媒绑定关系信息</el-divider>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名称">{{ socialBindingInfo.socialHandleName }}</el-descriptions-item>
              <el-descriptions-item label="平台">
                <el-tag :type="getPlatformTagType(socialBindingInfo.socialPlatform)">
                  {{ getPlatformLabel(socialBindingInfo.socialPlatform) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="社媒账号">{{ socialBindingInfo.socialUserId }}</el-descriptions-item>
              <el-descriptions-item label="社媒邮箱">{{ socialBindingInfo.socialEmail }}</el-descriptions-item>
            </el-descriptions>

            <!-- 社媒解绑操作 -->
            <div class="unbind-actions">
              <el-button
                type="danger"
                :loading="socialUnbindLoading"
                :disabled="!socialBindingInfo"
                @click="handleSocialUnbind"
              >
                <i class="el-icon-delete" /> 解绑社媒
              </el-button>
            </div>
          </div>

          <!-- 无社媒绑定关系提示 -->
          <div v-else-if="socialQueryExecuted && !socialBindingInfo" class="no-binding">
            <el-empty description="未找到社媒绑定关系">
              <el-button type="primary" @click="handleSocialReset">重新查询</el-button>
            </el-empty>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { queryWalletBinding, unbindWallet, querySocialBinding, unbindSocial } from '@/api/drex/socialUnbind/unbind'

export default {
  name: 'UnbindManagement',
  data() {
    return {
      activeTab: 'wallet',
      defaultForm: { id: null, handleName: null, walletAddress: null, socialHandleName: null, socialPlatform: null, socialUserId: null, socialEmail: null, walletType: null, connectType: null, connectedAt: null, customerId: null, bindingInfo: null },

      // 钱包相关数据
      walletQueryForm: {
        handleName: '',
        walletAddress: ''
      },
      walletQueryRules: {
        handleName: [
          { required: true, message: '请输入用户handleName', trigger: 'blur' }
        ],
        walletAddress: [
          { required: true, message: '请输入钱包地址', trigger: 'blur' },
          { min: 10, message: '钱包地址长度不能少于10位', trigger: 'blur' }
        ]
      },
      walletBindingInfo: null,
      walletQueryLoading: false,
      walletUnbindLoading: false,
      walletQueryExecuted: false,

      // 社媒相关数据
      socialQueryForm: {
        handleName: '',
        platform: ''
      },
      socialQueryRules: {
        handleName: [
          { required: true, message: '请输入用户handleName', trigger: 'blur' }
        ],
        platform: [
          { required: true, message: '请选择平台', trigger: 'change' }
        ]
      },
      socialBindingInfo: null,
      socialQueryLoading: false,
      socialUnbindLoading: false,
      socialQueryExecuted: false,

      // 平台选项
      platformOptions: [
        { value: 'X', label: 'X' },
        { value: 'Google', label: 'Google' },
        { value: 'Discord', label: 'Discord' },
        { value: 'Email', label: 'Email' },
        { value: 'Telegram', label: 'Telegram' },
        { value: 'Facebook', label: 'Facebook' },
        { value: 'Line', label: 'Line' },
        { value: 'Apple', label: 'Apple' }
      ]
    }
  },
  methods: {
    // 标签页切换
    handleTabClick(tab) {
      this.activeTab = tab.name
    },

    // 钱包查询绑定关系
    handleWalletQuery() {
      this.$refs.walletQueryForm.validate((valid) => {
        if (valid) {
          this.walletQueryLoading = true
          this.walletQueryExecuted = false

          queryWalletBinding({
            handleName: this.walletQueryForm.handleName,
            walletAddress: this.walletQueryForm.walletAddress
          }).then(response => {
            this.walletQueryExecuted = true
            if (response) {
              this.walletBindingInfo = response.bindingInfo
              this.$message.success('查询成功')
            } else {
              this.walletBindingInfo = null
              this.$message.warning('未找到钱包绑定关系')
            }
          }).catch(error => {
            this.walletQueryExecuted = true
            this.walletBindingInfo = null
            this.$message.error('查询失败：' + (error.message || '未知错误'))
          }).finally(() => {
            this.walletQueryLoading = false
          })
        }
      })
    },

    // 钱包解绑
    handleWalletUnbind() {
      this.$confirm('确定要解绑该钱包吗？解绑后将无法恢复！', '确认解绑', {
        confirmButtonText: '确定解绑',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.walletUnbindLoading = true

        unbindWallet({
          passportId: this.walletBindingInfo.passportId,
          walletAddress: this.walletBindingInfo.walletAddress
        }).then(response => {
          if (response === true) {
            this.$message.success('钱包解绑成功，可以重新查询确认是否删除')
            // 解绑成功后只清空绑定信息，保留输入框内容
            this.walletBindingInfo = null
            this.walletQueryExecuted = false
          } else {
            this.$message.error('解绑失败：未知错误')
          }
        }).catch(error => {
          this.$message.error('解绑失败：' + (error.message || '未知错误'))
        }).finally(() => {
          this.walletUnbindLoading = false
        })
      })
    },

    // 钱包重置表单
    handleWalletReset() {
      this.$refs.walletQueryForm.resetFields()
      this.walletBindingInfo = null
      this.walletQueryExecuted = false
    },

    // 社媒查询绑定关系
    handleSocialQuery() {
      this.$refs.socialQueryForm.validate((valid) => {
        if (valid) {
          this.socialQueryLoading = true
          this.socialQueryExecuted = false

          querySocialBinding({
            handleName: this.socialQueryForm.handleName,
            platform: this.socialQueryForm.platform
          }).then(response => {
            this.socialQueryExecuted = true
            if (response) {
              this.socialBindingInfo = response.bindingInfo
              this.$message.success('查询成功')
            } else {
              this.socialBindingInfo = null
              this.$message.warning('未找到社媒绑定关系')
            }
          }).catch(error => {
            this.socialQueryExecuted = true
            this.socialBindingInfo = null
            this.$message.error('查询失败：' + (error.message || '未知错误'))
          }).finally(() => {
            this.socialQueryLoading = false
          })
        }
      })
    },

    // 社媒解绑
    handleSocialUnbind() {
      this.$confirm('确定要解绑该社媒账号吗？', '确认解绑', {
        confirmButtonText: '确定解绑',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.socialUnbindLoading = true

        unbindSocial({
          passportId: this.socialBindingInfo.customerId,
          socialPlatform: this.socialBindingInfo.socialPlatform
        }).then(response => {
          if (response === true) {
            this.$message.success('社媒解绑成功，可以重新查询确认是否删除')
            // 解绑成功后只清空绑定信息，保留输入框内容
            this.socialBindingInfo = null
            this.socialQueryExecuted = false
          } else {
            this.$message.error('解绑失败：未知错误')
          }
        }).catch(error => {
          this.$message.error('解绑失败：' + (error.message || '未知错误'))
        }).finally(() => {
          this.socialUnbindLoading = false
        })
      })
    },

    // 社媒重置表单
    handleSocialReset() {
      this.$refs.socialQueryForm.resetFields()
      this.socialBindingInfo = null
      this.socialQueryExecuted = false
    },

    // 获取平台标签类型
    getPlatformTagType(platform) {
      const typeMap = {
        'X': 'primary',
        'Google': 'success',
        'Discord': 'info',
        'Email': 'warning',
        'Telegram': 'primary',
        'Facebook': 'success',
        'Line': 'info',
        'Apple': 'warning'
      }
      return typeMap[platform] || 'default'
    },

    // 获取平台标签文本
    getPlatformLabel(platform) {
      return platform || '未知平台'
    }
  }
}
</script>

<style scoped>
.query-form {
  margin-bottom: 20px;
}

.binding-info {
  margin-top: 20px;
}

.unbind-actions {
  margin-top: 20px;
  text-align: center;
}

.no-binding {
  margin-top: 20px;
  text-align: center;
}

.box-card {
  margin: 20px;
}

.app-container {
  padding: 20px;
}
</style>
