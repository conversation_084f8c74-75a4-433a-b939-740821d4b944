<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>风险评分配置</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          :loading="loading"
          @click="saveConfig"
        >
          保存配置
        </el-button>
      </div>

      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <!-- Twitter 配置 -->
        <el-tab-pane label="Twitter 欺诈指标配置" name="twitter">
          <el-form ref="twitterForm" :model="formData" class="config-form" label-position="top">
            <!-- 默认风险分 -->
            <el-form-item label="默认风险分：当分析系统异常无法计算风险分数时，默认返回的分数" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">默认风险分</span>
                  <el-input-number
                    v-model="formData.defaultRiskScore"
                    :min="0"
                    :max="100"
                    :step="1"
                    :precision="1"
                  />
                  <span class="form-tip">范围: 0 - 100</span>
                </div>
              </div>
            </el-form-item>

            <!-- 允许的Twitter风险分 -->
            <el-form-item label="允许的Twitter风险分：超过此分数将被视为欺诈" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">Twitter风险分</span>
                  <el-input-number
                    v-model="formData.allowedTwitterRiskScore"
                    :min="0"
                    :max="100"
                    :step="1"
                    :precision="0"
                  />
                  <span class="form-tip">范围: 0 - 100</span>
                </div>
              </div>
            </el-form-item>

            <el-divider content-position="left">Twitter指标配置</el-divider>

            <!-- Twitter 特定配置项 -->
            <el-form-item label="1. 相似度配置：检测推文内容相似度是否异常" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.similarityConfig.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.similarityConfig.threshold"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.similarityConfig.maxAllowed"
                    :min="formData.indicatorConfig.similarityConfig.threshold"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <el-form-item label="2. 简洁度配置：检测推文内容是否过于简单" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.simplicityConfig.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.simplicityConfig.maxAllowed"
                    :min="0"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大句子长度:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.simplicityConfig.maxSentenceLength"
                    :min="1"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最小句子长度:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.simplicityConfig.minSentenceLength"
                    :min="1"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.simplicityConfig.threshold"
                    :min="0"
                    :max="formData.indicatorConfig.simplicityConfig.maxAllowed"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <el-form-item label="3. 回放配置：检测是否可能是回放攻击" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.replayConfig.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.replayConfig.threshold"
                    :min="0"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.replayConfig.maxAllowed"
                    :min="formData.indicatorConfig.replayConfig.threshold"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <div class="form-actions">
              <el-button type="primary" :loading="loading" @click="saveConfig('twitter')">保存 Twitter 配置</el-button>
            </div>

          </el-form>
        </el-tab-pane>

        <!-- YouTube 配置 -->
        <el-tab-pane label="YouTube 欺诈指标配置" name="youtube">
          <el-form ref="youtubeForm" :model="formData" class="config-form" label-position="top">
            <!-- 默认风险分 -->
            <el-form-item label="默认风险分：当分析系统异常无法计算风险分数时，默认返回的分数" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">默认风险分</span>
                  <el-input-number
                    v-model="formData.defaultRiskScore"
                    :min="0"
                    :max="100"
                    :step="1"
                    :precision="1"
                  />
                  <span class="form-tip">范围: 0 - 100</span>
                </div>
              </div>
            </el-form-item>

            <!-- 允许的YouTube风险分 -->
            <el-form-item label="允许的YouTube风险分：超过此分数将被视为欺诈" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">YouTube风险分</span>
                  <el-input-number
                    v-model="formData.allowedYouTubeRiskScore"
                    :min="0"
                    :max="100"
                    :step="1"
                    :precision="0"
                  />
                  <span class="form-tip">范围: 0 - 100</span>
                </div>
              </div>
            </el-form-item>

            <el-divider content-position="left">YouTube指标配置</el-divider>

            <!-- 重复事件 -->
            <el-form-item label="1. 重复事件：检测前端上报的事件中是否存在异常的重复事件（例如短时间内重复触发 play、pause 或其他事件，可能表示脚本刷量行为）" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.repeatedEvent.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.repeatedEvent.threshold"
                    :min="0"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.repeatedEvent.maxAllowed"
                    :min="formData.indicatorConfig.repeatedEvent.threshold"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 完成度 -->
            <el-form-item label="2. 完成百分比：检测用户在视频播放期间的页面聚焦时间占比是否过低（例如视频播放时页面处于非活跃状态，可能表示挂机）" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.completion.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">预期完成度:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.completion.expectedPercentage"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许完成度:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.completion.maxAllowedPercentage"
                    :min="formData.indicatorConfig.completion.expectedPercentage"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 专注度 -->
            <el-form-item label="3. 低聚焦百分比：检测用户在视频播放期间的页面聚焦时间占比是否过低（例如视频播放时页面处于非活跃状态，可能表示挂机）" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.focus.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.focus.threshold"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最小允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.focus.minAllowed"
                    :min="0"
                    :max="formData.indicatorConfig.focus.threshold"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 闲置时间 -->
            <el-form-item label="4. 长时间空闲：检测用户在视频播放期间是否长时间处于空闲状态（activity_state = IDLE），可能表示无人操作或挂机" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.idle.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.idle.threshold"
                    :min="0"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.idle.maxAllowed"
                    :min="formData.indicatorConfig.idle.threshold"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 环境检测 -->
            <el-form-item label="5. 环境不一致：检测设备指纹 (device_finger) 和 IP 地址之间的匹配是否异常（例如同一指纹频繁更换 IP，可能表示使用代理或多设备刷量）" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.environment.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">账户阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.environment.accountsThreshold"
                    :min="0"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">账户最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.environment.accountsMaxAllowed"
                    :min="formData.indicatorConfig.environment.accountsThreshold"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">IP变更阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.environment.ipChangesThreshold"
                    :min="0"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">IP变更最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.environment.ipChangesMaxAllowed"
                    :min="formData.indicatorConfig.environment.ipChangesThreshold"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 时间戳 -->
            <el-form-item label="6. 时间戳异常：检测客户端时间戳与服务器时间戳差异，可能表示伪造" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.timestamp.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大时间差(秒):</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.timestamp.maxDiffSeconds"
                    :min="0"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 播放速度 -->
            <el-form-item label="7. 播放速度：检测播放速率是否偏离正常值（通常为 1.0）" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.playbackSpeed.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.playbackSpeed.threshold"
                    :min="0.1"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.playbackSpeed.maxAllowed"
                    :min="formData.indicatorConfig.playbackSpeed.threshold"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 事件顺序 -->
            <el-form-item label="8. 事件顺序：检测事件时间线不合理（如 pause 先于 play）" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.eventOrder.weight"
                    :min="0"
                    :max="1"
                    :step="0.001"
                    :precision="3"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 异常跳转 -->
            <el-form-item label="9. 异常跳转：检测跳转次数或跳转到未解锁部分" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.abnormalSeek.weight"
                    :min="0"
                    :max="1"
                    :step="0.001"
                    :precision="3"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.abnormalSeek.threshold"
                    :min="0"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.abnormalSeek.maxAllowed"
                    :min="formData.indicatorConfig.abnormalSeek.threshold"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 指纹重复 -->
            <el-form-item label="10. 指纹重复：检测同一设备指纹关联多个账号" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.fingerprintDuplication.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">账户阈值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.fingerprintDuplication.accountsThreshold"
                    :min="0"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
                <div class="control-group">
                  <span class="label">账户最大允许值:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.fingerprintDuplication.accountsMaxAllowed"
                    :min="formData.indicatorConfig.fingerprintDuplication.accountsThreshold"
                    :step="1"
                    :precision="0"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 恶意IP -->
            <el-form-item label="11. 恶意IP：检测 IP 是否为代理/VPN/Tor" class="indicator-item">
              <div class="config-item">
                <div class="control-group">
                  <span class="label">权重:</span>
                  <el-input-number
                    v-model="formData.indicatorConfig.maliciousIp.weight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    controls-position="right"
                  />
                </div>
              </div>
            </el-form-item>

            <div class="form-actions">
              <el-button type="primary" :loading="loading" @click="saveConfig('youtube')">保存 YouTube 配置</el-button>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { getRiskConfig, updateRiskConfig } from '@/api/drex/risk/risk'

export default {
  name: 'RiskConfig',
  data() {
    const initialFormData = {
      defaultRiskScore: 0,
      allowedTwitterRiskScore: 90,
      allowedYouTubeRiskScore: 90,
      indicatorConfig: {
        repeatedEvent: { weight: 0, threshold: 0, maxAllowed: 0 },
        completion: { weight: 0, expectedPercentage: 0, maxAllowedPercentage: 0 },
        focus: { weight: 0, threshold: 0, minAllowed: 0 },
        idle: { weight: 0, threshold: 0, maxAllowed: 0 },
        environment: {
          weight: 0,
          accountsThreshold: 0,
          accountsMaxAllowed: 0,
          ipChangesThreshold: 0,
          ipChangesMaxAllowed: 0
        },
        timestamp: { weight: 0, maxDiffSeconds: 0 },
        playbackSpeed: { weight: 0, threshold: 0, maxAllowed: 0 },
        abnormalSeek: { weight: 0, threshold: 0, maxAllowed: 0 },
        fingerprintDuplication: {
          weight: 0,
          accountsThreshold: 0,
          accountsMaxAllowed: 0
        },
        maliciousIp: { weight: 0 },
        eventOrder: { weight: 0 },
        similarityConfig: { weight: 0, threshold: 0, maxAllowed: 0 },
        simplicityConfig: {
          fleschScore: 0,
          gradeLevel: 0,
          avgSentenceLength: 0,
          weight: 0
        },
        replayConfig: { weight: 0, threshold: 0, maxAllowed: 0 }
      }
    }

    return {
      loading: false,
      formData: JSON.parse(JSON.stringify(initialFormData)), // Deep clone to avoid reference issues
      activeTab: 'twitter' // 设置默认激活的tab为twitter
    }
  },
  watch: {
    'formData.indicatorConfig.playbackSpeed.minSpeed'(newVal) {
      if (newVal >= this.formData.indicatorConfig.playbackSpeed.maxSpeed) {
        this.formData.indicatorConfig.playbackSpeed.maxSpeed = newVal + 0.1
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    handleTabClick(tab) {
      // 标签页切换时的处理逻辑
      console.log('切换到标签页:', tab.name)
    },
    resetFormToZeros() {
      // Reset form to all zeros with new structure
      this.formData = {
        defaultRiskScore: 0,
        allowedTwitterRiskScore: 90,
        allowedYouTubeRiskScore: 90,
        indicatorConfig: {
          playbackSpeed: { maxAllowed: 1.5, weight: 0.3, threshold: 1.2 },
          completion: { expectedPercentage: 0.9, weight: 0.05, maxAllowedPercentage: 1.5 },
          environment: {
            accountsThreshold: 2.0,
            weight: 0.05,
            accountsMaxAllowed: 5.0,
            ipChangesThreshold: 2.0,
            ipChangesMaxAllowed: 5.0
          },
          abnormalSeek: { maxAllowed: 10.0, weight: 0.025, threshold: 5.0 },
          idle: { maxAllowed: 0.5, weight: 0.05, threshold: 0.2 },
          focus: { minAllowed: 0.3, weight: 0.15, threshold: 0.7 },
          maliciousIp: { weight: 0.15 },
          repeatedEvent: { maxAllowed: 10.0, weight: 0.05, threshold: 5.0 },
          eventOrder: { weight: 0.025 },
          fingerprintDuplication: {
            accountsThreshold: 2.0,
            weight: 0.1,
            accountsMaxAllowed: 5.0
          },
          timestamp: { weight: 0.05, maxDiffSeconds: 5.0 },
          similarityConfig: { weight: 0.35, threshold: 0.85, maxAllowed: 0.92 },
          simplicityConfig: {
            maxAllowed: 1.0,
            maxSentenceLength: 25,
            minSentenceLength: 5,
            threshold: 0.8,
            weight: 0.5
          },
          replayConfig: { weight: 0.25, threshold: 0.8, maxAllowed: 1.1 }
        }
      }
    },
    async fetchData() {
      this.loading = true
      try {
        // Reset form to all zeros
        this.resetFormToZeros()

        // Fetch data from API - the response is the actual data, not wrapped in data property
        const apiData = await getRiskConfig()
        console.log('API Response Data:', apiData)

        if (apiData) {
          // Directly assign the API data to formData
          this.formData = {
            defaultRiskScore: Number(apiData.defaultRiskScore) || 0,
            allowedTwitterRiskScore: Number(apiData.allowedTwitterRiskScore) || 90,
            allowedYouTubeRiskScore: Number(apiData.allowedYouTubeRiskScore) || 90,
            indicatorConfig: { ...apiData.indicatorConfig }
          }

          // Ensure all numeric values are properly converted
          if (this.formData.indicatorConfig) {
            Object.keys(this.formData.indicatorConfig).forEach(section => {
              const sectionData = this.formData.indicatorConfig[section]
              if (sectionData) {
                Object.keys(sectionData).forEach(key => {
                  if (typeof sectionData[key] === 'number') {
                    sectionData[key] = Number(sectionData[key])
                  }
                })
              }
            })
          }

          console.log('Updated form data:', this.formData)
        } else {
          console.warn('No data received from API')
        }
      } catch (error) {
        console.error('Failed to fetch risk config:', error)
        this.$message.error('获取配置失败: ' + (error.message || '未知错误'))
        // Ensure form is reset to zeros on error
        this.resetFormToZeros()
      } finally {
        this.loading = false
      }
    },
    isObject(item) {
      return (item && typeof item === 'object' && !Array.isArray(item))
    },
    async saveConfig(platform) {
      try {
        this.loading = true
        // Create a copy of formData to avoid modifying the original directly
        const formDataCopy = JSON.parse(JSON.stringify(this.formData))

        // Ensure all numeric values are properly converted
        Object.keys(formDataCopy.indicatorConfig).forEach(section => {
          const sectionData = formDataCopy.indicatorConfig[section]
          if (sectionData && typeof sectionData === 'object') {
            Object.keys(sectionData).forEach(key => {
              if (typeof sectionData[key] === 'number') {
                sectionData[key] = Number(sectionData[key])
              }
            })
          }
        })

        await updateRiskConfig(formDataCopy)
        this.$message.success(`${platform === 'twitter' ? 'Twitter' : 'YouTube'} 配置保存成功`)
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error(`保存${platform === 'twitter' ? 'Twitter' : 'YouTube'}配置失败: ${error.message || '未知错误'}`)
      } finally {
        this.loading = false
      }
    },
    getLabel(key) {
      return this.labelMap[key] || key
    }
  }
}
</script>

<style scoped>
.config-form {
  max-width: 1000px;
  margin: 0 auto;
}

/* 重置所有表单项的标签样式 */
:deep(.el-form-item) {
  --el-form-label-font-size: 14px;
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  float: none;
  text-align: left !important;
  width: 100% !important;
  padding: 0 0 8px 0 !important;
  margin: 0 0 10px 0 !important;
  font-weight: 500;
  line-height: 1.5;
  color: #303133;
  border-bottom: 1px solid #e0e0e0;
  display: block;
}

/* 指标项容器样式 */
.indicator-item {
  margin-bottom: 24px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f8f9fa;
}

/* 指标项标题样式 */
.indicator-item :deep(.el-form-item__label) {
  text-align: left !important;
  font-size: 15px;
  color: #303133;
  padding-bottom: 8px;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
  width: 100%;
  display: block;
}

/* 配置项容器 */
.config-item {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

/* 控制组样式 */
.control-group {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 12px;
}

/* 标签样式 */
.label {
  min-width: 100px;
  color: #606266;
  font-size: 13px;
  text-align: left;
  margin-right: 10px;
  white-space: nowrap;
}

/* 提示文字 */
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
}

/* 分隔线 */
.el-divider {
  margin: 24px 0;
}

/* 数字输入框 */
.el-input-number {
  width: 140px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .config-item {
    flex-direction: column;
    gap: 12px;
  }

  .control-group {
    width: 100%;
    margin-right: 0;
  }

  .label {
    min-width: 80px;
  }

  .el-input-number {
    width: 100%;
  }
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}
</style>
