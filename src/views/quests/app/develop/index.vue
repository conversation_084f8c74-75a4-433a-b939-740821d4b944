<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">app</label>
        <el-input v-model="query.app" clearable placeholder="app" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="app" prop="app">
            <el-input v-model="form.app" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="api_key">
            <el-input v-model="form.apiKey" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="api_secret">
            <el-input v-model="form.apiSecret" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="client_id">
            <el-input v-model="form.clientId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="client_secret">
            <el-input v-model="form.clientSecret" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="bearer_token">
            <el-input v-model="form.bearerToken" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="app" label="app" />
        <el-table-column prop="apiKey" label="api_key" />
        <el-table-column prop="apiSecret" label="api_secret" />
        <el-table-column prop="clientId" label="client_id" />
        <el-table-column prop="clientSecret" label="client_secret" />
        <el-table-column prop="bearerToken" label="bearer_token" />
        <el-table-column v-if="checkPer(['admin','appDeveloperManage:edit','appDeveloperManage:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudAppDeveloperManage from '@/api/appDeveloperManage'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { app: null, apiKey: null, apiSecret: null, clientId: null, clientSecret: null, bearerToken: null, id: null }
export default {
  name: 'AppDeveloperManage',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'AppDeveloperManage', url: 'api/appDeveloperManage', idField: 'id', sort: 'id,desc', crudMethod: { ...crudAppDeveloperManage }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'appDeveloperManage:add'],
        edit: ['admin', 'appDeveloperManage:edit'],
        del: ['admin', 'appDeveloperManage:del']
      },
      rules: {
        app: [
          { required: true, message: 'app不能为空', trigger: 'blur' }
        ],
        id: [
          { required: true, message: 'id不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'app', display_name: 'app' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
