<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">任务事件</label>
        <el-input v-model="query.code" clearable placeholder="任务事件" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">主任务事件</label>
        <el-input v-model="query.mainCode" clearable placeholder="主任务事件" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">增量值</label>
        <el-input v-model="query.inc" clearable placeholder="增量值" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式，slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="任务事件" prop="code">
            <el-input v-model="form.code" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="主任务事件" prop="mainCode">
            <el-input v-model="form.mainCode" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="增量值" prop="inc">
            <el-input v-model="form.inc" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="任务事件描述" prop="desc">
            <el-input v-model="form.desc" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="拆分子事件" prop="splitCode">
            <el-input v-model="form.splitCode" style="width: 370px;" placeholder="事件间已英文都好分隔，被动事件已下划线结束" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="任务事件" />
        <el-table-column prop="mainCode" label="主任务事件" />
        <el-table-column prop="inc" label="增量值" />
        <el-table-column prop="desc" label="任务事件描述" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="modifiedTime" label="修改时间" />
        <el-table-column prop="splitCode" label="拆分子事件" />
        <el-table-column v-if="checkPer(['admin','taskCode:edit','taskCode:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTaskCode from '@/api/quests/task/code/taskCode'
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation.vue'
import pagination from '@crud/Pagination.vue'
import rrOperation from '@crud/RR.operation.vue'
import udOperation from '@crud/UD.operation.vue'

const defaultForm = { id: null, code: null, mainCode: null, inc: null, desc: null, createTime: null, modifiedTime: null, splitCode: null }
export default {
  name: 'TaskCode',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '任务事件配置', url: 'api/taskCode', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTaskCode }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'taskCode:add'],
        edit: ['admin', 'taskCode:edit'],
        del: ['admin', 'taskCode:del']
      },
      rules: {
        code: [
          { required: true, message: '任务事件不能为空', trigger: 'blur' }
        ],
        mainCode: [
          { required: true, message: '主任务事件不能为空', trigger: 'blur' }
        ],
        inc: [
          { required: true, message: '增量值不能为空', trigger: 'blur' }
        ],
        desc: [
          { required: true, message: '任务事件描述不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'code', display_name: '任务事件' },
        { key: 'mainCode', display_name: '主任务事件' },
        { key: 'inc', display_name: '增量值' },
        { key: 'splitCode', display_name: '拆分子事件' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
