<template>
  <div class="app-container">

    <!-- 表单区域 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>添加/编辑中奖名单</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" inline label-width="80px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="所属项目" prop="saasId">
              <el-select v-model="form.saasId" filterable placeholder="选择项目">
                <el-option
                  v-for="item in dict.saas_id"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="活动编号" prop="activityId">
              <el-select v-model="form.activityId" filterable placeholder="选择活动编号">
                <el-option
                  v-for="item in dict.activity_code"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="奖池编号" prop="poolId">
              <el-select v-model="form.poolId" filterable placeholder="选择奖池编号">
                <el-option
                  v-for="item in dict.lottery_pool_code"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="中奖用户" prop="prizeAddress">
              <el-input v-model="form.prizeAddress" placeholder="请输入中奖用户" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="中奖序号" prop="prizeOrder">
              <el-input v-model="form.prizeOrder" placeholder="请输入中奖序号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              &nbsp;&nbsp;&nbsp;<el-button type="primary" @click="submitForm">提交</el-button>
              <el-button style="margin-left: 10px" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜索区域 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>中奖名单列表</span>
      </div>
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="所属项目" prop="saasId">
          <el-select v-model="searchForm.saasId" filterable placeholder="选择项目">
            <el-option
              v-for="item in dict.saas_id"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动编号" prop="activityId">
          <el-select v-model="searchForm.activityId" filterable placeholder="选择活动编号">
            <el-option
              v-for="item in dict.activity_code"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="button" class="el-button filter-item el-button--success el-button--mini" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="所属项目" prop="saasId" align="center" />
        <el-table-column label="活动编号" prop="activityId" align="center" />
        <el-table-column label="奖池编号" prop="poolId" align="center" />
        <el-table-column label="中奖地址" prop="prizeAddress" align="center" />
        <el-table-column label="中奖序号" prop="prizeOrder" align="center" />
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="{ row }">
            <el-button type="primary" size="mini" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <!-- <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="listQuery.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="listQuery.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div> -->
    </el-card>

  </div>
</template>

<script>
import { add, del, queryList } from '@/api/quests/task/winnerlist/winnerlist'

export default {
  dicts: ['intboolean', 'task_type', 'activity_code', 'lottery_pool_code', 'saas_id', 'boolean', 'task_status', 'task_cycle', 'progress_type', 'reward_form', 'provide_type', 'reward_type', 'btn_show', 'task_channel', 'task_status_condition', 'client_type', 'domain', 'event_code'],
  data() {
    return {
      searchForm: {
        saasId: '',
        activityId: ''
      },
      form: {
        saasId: '',
        activityId: '',
        poolId: '',
        prizeAddress: '',
        prizeOrder: ''
      },
      rules: {
        saasId: [
          { required: true, message: '请输入所属项目', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '请输入活动编号', trigger: 'blur' }
        ],
        poolId: [
          { required: true, message: '请输入奖池编号', trigger: 'blur' }
        ],
        prizeAddress: [
          { required: true, message: '请输入中奖地址', trigger: 'blur' }
        ],
        prizeOrder: [
          { required: true, message: '请输入中奖序号', trigger: 'blur' }
        ]
      },
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList() {
      const params = {
        ...this.listQuery,
        ...this.searchForm
      }
      queryList(params).then(response => {
        console.log(response)
        if (response && response.length > 0) {
          this.list = response.map(item => ({
            saasId: item.saasId,
            activityId: item.activityId,
            poolId: item.poolId,
            prizeAddress: item.prizeAddress,
            prizeOrder: item.prizeOrder
          }))
          this.total = response.length
          this.$message({
            message: '数据加载成功',
            type: 'success'
          })
        } else {
          this.list = []
          this.total = 0
          this.$message({
            message: '暂无数据',
            type: 'info'
          })
        }
      }).catch(error => {
        this.list = []
        this.total = 0
        this.$message.error(error.message || '数据加载失败')
      })
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          add(this.form).then(response => {
            const success = response
            if (success) {
              this.$message({
                message: '操作成功',
                type: 'success'
              })
              this.resetForm()
              this.getList()
            } else {
              this.$message({
                message: '操作失败',
                type: 'error'
              })
            }
          }).catch(error => {
            this.$message.error(error.message)
          })
        }
      })
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
    },
    // 处理搜索
    handleSearch() {
      this.listQuery.page = 1
      this.getList()
    },
    // 重置搜索
    handleResetSearch() {
      this.$refs.searchForm.resetFields()
      this.getList()
    },
    // 编辑
    handleEdit(row) {
      this.form = Object.assign({}, row)
    },
    // 删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          saasId: row.saasId,
          activityId: row.activityId,
          poolId: row.poolId,
          prizeAddress: row.prizeAddress
        }
        del(params).then(response => {
          const success = response
          if (success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          } else {
            this.$message({
              type: 'error',
              message: '删除失败!'
            })
          }
        }).catch(error => {
          this.$message.error(error.message || '删除失败')
        })
      })
    },

    // 分页处理
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    }
  }
}

</script>

<style scoped>
.app-container {
  padding: 20px;
}
.box-card {
  margin-bottom: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
