<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">appId</label>
        <el-select v-model="query.appId" clearable size="small" placeholder="appId" class="filter-item" style="width: 100px" @change="crud.toQuery">
          <el-option v-for="item in dict.app_id" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>

        <label class="el-form-item-label">任务 id</label>
        <el-input v-model="query.taskId" clearable placeholder="任务id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">事件code</label>
        <el-input v-model="query.code" clearable placeholder="事件code" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">标题</label>
        <el-input v-model="query.title" clearable placeholder="标题(模糊查询)" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />

        <el-drawer
          title="我是里面的"
          :with-header="false"
          :append-to-body="true"
          :visible.sync="innerAllDrawer"
          :direction="direction"
          :size="size"
        >
          <json-viewer
            :value="innerAllDrawerDate"
            :expand-depth="5"
            copyable
          />
        </el-drawer>

        <el-dialog title="json内容" :visible.sync="dialogFormVisible" center>
          <el-form>
            <el-form-item label="import json" :label-width="formLabelWidth">
              <el-input v-model="importJSONDate" type="textarea" :rows="25" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button :loading="loading == false" type="primary" @click="importJSON">确 定</el-button>
          </div>
        </el-dialog>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式，slot = 'left' or 'right'-->
      <crudOperation :permission="permission">
        <el-button slot="right" :loading="loading == false" type="primary" class="filter-item" size="mini" @click="exportAllJson">导出json</el-button>
        <el-button slot="right" :loading="loading == false" type="primary" class="filter-item" size="mini" @click="dialogFormVisible = true">导入json</el-button>
      </crudOperation>
      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        :title-align="'center'"
        width="900px"
      >
        <div class="wrapper">
          <div style="margin:30px" class="my-div">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item title="基本配置" name="basic">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="90px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="项目id" prop="appId">
                        <el-select v-model="form.appId" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.app_id"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="开始时间">
                        <el-date-picker v-model="form.startTime" type="datetime" style="width: 100%;" filterable placeholder="任务开始时间" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="结束时间">
                        <el-date-picker v-model="form.endTime" type="datetime" style="width: 100%;" filterable placeholder="任务结束时间" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-form-item label="任务事件">
                        <el-select v-model="form.code" filterable placeholder="任务事件">
                          <el-option
                            v-for="item in dict.event_code"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="区域或模块">
                        <el-tooltip class="item-tooltip" effect="dark" content="domain, 任务显示到页面的模块标识，通常由前端决定" placement="top">
                          <el-select v-model="form.domain" filterable placeholder="请选择">
                            <el-option
                              v-for="item in dict.domain"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="列表可见">
                        <el-select v-model="form.showList" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务排序">
                        <el-tooltip class="item-tooltip" effect="dark" content="order, 值越小越靠前" placement="top">
                          <el-input v-model="form.order" placeholder="排序值" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务上限">
                        <el-input v-model="form.limitCountNormal" filterable placeholder="任务次数上限" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="按钮显示">
                        <el-select v-model="form.btn" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.btn_show"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="进度显示">
                        <el-select v-model="form.showProgress" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="刷新周期">
                        <el-select v-model="form.cycle" filterable placeholder="任务刷新周期">
                          <el-option
                            v-for="item in dict.task_cycle"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="文案配置" name="detail">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="8" style="width: 45%;">
                      <el-form-item label="标题">
                        <el-input v-model="form.title" filterable placeholder="标题" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8" style="width: 45%;">
                      <el-form-item label="描述">
                        <el-input v-model="form.desc" filterable placeholder="描述" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="积分流水">
                        <el-input v-model="form.ledgerTitle" placeholder="积分流水描述" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 90%;">
                      <el-form-item label="icon">
                        <el-upload
                          v-model="form.icon"
                          :headers="headers"
                          :action="taskIconUploadApi"
                          list-type="picture-card"
                          :data="{appId: form.appId, code: form.showCode !== null ? form.showCode : form.code}"
                          :on-success="cropUploadSuccess"
                        >
                          <i class="el-icon-plus" />
                        </el-upload>
                        <img width="100%" :src="form.icon" alt="">
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="奖励配置" name="reward">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="发奖频率">
                        <el-tooltip class="item-tooltip" effect="dark" content="每完成n次任务，发一次奖，1表示每完成1次发奖一次" placement="top">
                          <el-input v-model="form.rewardFrequency" filterable placeholder="每完成n次任务，发一次奖" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="奖励数量">
                        <el-input v-model="form.rewardAmount" placeholder="奖励数量" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="奖励类型">
                        <el-select v-model="form.rewardType" filterable placeholder="奖励类型">
                          <el-option
                            v-for="item in dict.reward_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="展示奖励数量">
                        <el-input v-model="form.showRewardAmount" placeholder="展示奖励数量" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="奖励币种">
                        <el-input v-model="form.rewardCurrency" placeholder="默认同奖励类型" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-tooltip class="item-tooltip" effect="dark" content="多个奖励点击此按钮新增" placement="top">
                        <el-button type="primary" @click.prevent="addDomain()">增加奖励</el-button>
                      </el-tooltip>
                    </el-col>
                  </el-row>
                  <el-form-item
                    v-for="(reward, index) in form.rewards"
                    :key="reward.key"
                    :index="index"
                    :prop="'reward.' + index + '.value'"
                  >
                    <el-row>
                      <el-col :span="8">
                        <el-form-item label="奖励数量">
                          <el-input v-model="reward.rewardAmount" placeholder="奖励数量" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="奖励类型">
                          <el-select v-model="reward.rewardType" filterable placeholder="奖励内容">
                            <el-option
                              v-for="item in dict.reward_type"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="奖励币种">
                          <el-input v-model="reward.rewardCurrency" placeholder="默认同奖励类型" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-button type="danger" @click.prevent="removeDomain(reward)">删除</el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-form>
              </el-collapse-item>
              <el-collapse-item title="其他配置" name="other">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="98px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="任务详情">
                        <el-input v-model="form.url" placeholder="任务详情页链接" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务额外属性">
                        <el-input v-model="form.attr" placeholder="任务额外属性" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务授权链接">
                        <el-input v-model="form.connectUrl" placeholder="任务授权链接" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="default" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="appId" label="所属项目" width="100">
          <template slot-scope="scope">
            {{ dict.label.app_id[scope.row.appId] }}
          </template>
        </el-table-column>
        <el-table-column prop="taskId" label="任务id" width="120" />
        <el-table-column prop="order" label="顺序(小->大)" width="100" />
        <el-table-column prop="code" label="任务事件" width="200" />
        <el-table-column prop="title" label="任务标题" width="320" />
        <el-table-column prop="status" label="任务状态" width="100">
          <template slot-scope="scope">
            {{ dict.label.task_status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="计划发布时间" width="170" />
        <el-table-column prop="startTime" label="开始时间" width="170" />
        <el-table-column label="配置详情" style="width: 50px;" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="default" @click="taskConfigDetail(scope.row)">查看</el-button>
            <el-drawer
              title="任务配置详情"
              :with-header="false"
              :visible.sync="drawer"
              :direction="direction"
              :size="size"
              @close="closeDrawer"
            >
              <div class="custom-table">
                <el-table
                  :data="baseData"
                  border
                  style="width: 90%; margin: 18px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="基本配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="detailData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="详细配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="otherConfigData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="其他配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>
              </div>
              <el-button :loading="loading == false" type="primary" style="margin-left: 16px;" @click="exportJson">
                导出json
              </el-button>
              <el-drawer
                title="我是里面的"
                :with-header="false"
                :visible.sync="innerDrawer"
                :append-to-body="true"
              >
                <json-viewer
                  :value="innerDrawerDate"
                  :expand-depth="5"
                  copyable
                />
              </el-drawer>
            </el-drawer>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','taskConfig:edit','taskConfig:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>

        <el-table-column label="发布操作" align="center" width="170">
          <template slot-scope="scope">
            <div style="display: flex; gap: 2px; margin-right: 3px;">
              <el-dropdown split-button type="primary" size="mini" @command="handlePublishCommand" @click="releasePublish(scope.row)">
                立即发布
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="beforePublishCommand('grey', scope.row)">灰度发布</el-dropdown-item>
                  <el-dropdown-item :command="beforePublishCommand('timing', scope.row)">定时发布</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-dialog title="定时发布" :visible.sync="dialogTimingVisible" center width="25%">
                <el-form ref="timingForm" :model="timingForm">
                  <el-form-item label="发布时间" :label-width="formLabelWidth">
                    <el-date-picker
                      v-model="timingForm.time"
                      type="datetime"
                      placeholder="选择日期时间"
                    >
                      <el-input slot="prepend" v-model="timingForm.taskId" readonly />
                    </el-date-picker>
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <el-button type="primary" @click="timingPublish">确 定</el-button>
                </div>
              </el-dialog>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTaskConfig, { exportAllJson, exportJson, greyPublish, importJSON, releasePublish, timingPublish } from '@/api/quests/task/config/taskConfig'

import { getToken } from '@/utils/auth'
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
import JsonViewer from 'vue-json-viewer'
import { mapGetters } from 'vuex'

const defaultForm = { showRewardAmount: null, appId: null, id: null, taskId: null, showList: null, ledgerTitle: null, title: null, desc: null, status: null, startTime: null, endTime: null, code: null, showCode: null, limit: null, rewardFrequency: null, cycle: null, progressType: null, rewardForm: null, provideType: null, rewardType: null, rewardAmount: null, rewardCurrency: null, rewardVipLevel: null, rewardIndex: null, order: null, domain: null, vipLevel: null, btn: null, showProgress: null, url: null, attr: null, connectUrl: null, rewards: [] }
export default {
  name: 'TaskConfig',
  components: { pagination, crudOperation, rrOperation, udOperation, JsonViewer },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['intboolean', 'task_type', 'app_id', 'boolean', 'task_status', 'task_cycle', 'progress_type', 'reward_form', 'provide_type', 'reward_type', 'btn_show', 'task_channel', 'task_status_condition', 'client_type', 'domain', 'event_code'],
  cruds() {
    return CRUD({ title: '任务配置接口', url: 'api/taskConfig', idField: 'id', sort: 'appId,domain,order', crudMethod: { ...crudTaskConfig }})
  },
  data() {
    return {
      headers: {
        'Authorization': getToken()
      },
      formLabelWidth: 20,
      dialogTimingVisible: false,
      timingForm: {
        time: null,
        taskId: null
      },
      loading: true,
      importJSONDate: null,
      dialogFormVisible: false,
      taskId: null,
      innerDrawer: false,
      innerDrawerDate: null,
      innerAllDrawer: false,
      innerAllDrawerDate: null,
      dialogVisible: false,
      show: false,
      activeNames: ['basic'], // 默认展开的项
      drawer: false,
      size: '45%',
      direction: 'rtl',
      baseData: [],
      detailData: [],
      domainConfigData: [],
      otherConfigData: [],
      selectedFormulae: 'formulae_redio_1',
      computeFormula: '',
      formulaeMap: {
        formulae_1: '',
        formulae_2: "#map['param_a'] * param_b + param_c",
        formulae_3: "#map['param_a'] / param_b + param_c",
        formulae_4: "#map['param_a'] * param_b > param_c ? param_d : param_e"
      },
      formulaeParams: {
        param_a: '',
        param_b: '',
        param_c: '',
        param_d: '',
        param_e: ''
      },
      permission: {
        add: ['admin', 'taskConfig:add'],
        edit: ['admin', 'taskConfig:edit'],
        del: ['admin', 'taskConfig:del']
      },
      rules: {
        appId: [
          { required: true, message: 'appId 不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'appId', display_name: 'appId' },
        { key: 'taskId', display_name: '任务 id' },
        { key: 'title', display_name: '任务标题' },
        { key: 'status', display_name: '任务状态' }
      ]
    }
  },
  methods: {
    beforePublishCommand(action, value) {
      return {
        'action': action,
        'value': value
      }
    },
    removeDomain(item) {
      var index = this.form.rewards.indexOf(item)
      console.info(index)
      if (index !== -1) {
        this.form.rewards.splice(index, 1)
      }
    },
    addDomain() {
      this.form.rewards.push({
        value: '',
        key: Date.now()
      })
    },
    // 任务配置详情
    taskConfigDetail(row) {
      this.drawer = true
      this.taskId = row.taskId
      this.baseData = [
        { key: 'appId', value: row.appId },
        { key: '任务 id', value: row.taskId },
        { key: '任务状态', value: row.status },
        { key: '所属模块', value: row.domain },
        { key: '任务标题', value: row.title }
      ]
      this.detailData = [
        { key: '列表展示', value: row.showList },
        { key: '任务排序', value: row.order },
        { key: '任务要求 vip 等级', value: row.vipLevel },
        { key: '积分流水描述', value: row.ledgerTitle },
        { key: '任务事件', value: row.code },
        { key: '开始时间', value: row.startTime },
        { key: '结束时间', value: row.endTime },
        { key: '任务描述', value: row.desc },
        { key: '任务次数', value: '',
          children: [
            { key: '任务次数上限 (非会员)', value: row.limitCountNormal },
            { key: '任务次数上限（会员 L1)', value: row.limitCountL1 }]
        },
        { key: '奖励配置', value: '',
          children: [
            { key: '发奖频率', value: row.rewardFrequency },
            { key: '刷新周期', value: row.cycle },
            { key: '奖品类型', value: row.rewardType },
            { key: '奖励数量', value: row.rewardAmount },
            { key: '奖励币种', value: row.rewardCurrency },
            { key: '展示奖励数量', value: row.showRewardAmount }
          ]
        }
      ]

      this.otherConfigData = [
        { key: '前端显示按钮', value: row.btn },
        { key: '显示任务进度条', value: row.showProgress },
        { key: '任务详情页', value: row.url },
        { key: '任务额外属性', value: row.attr },
        { key: '任务授权链接', value: row.connectUrl }
      ]
    },
    releasePublish(row) {
      console.info(row)
      this.$confirm(`确定立即发布任务 ${row.taskId} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        releasePublish(row).then(result => {
          this.crud.notify(result, 'success')
          // 刷新当前页面，保留搜索条件
          this.crud.refresh()
        })
      })
    },
    handlePublishCommand(row) {
      if (row.action === 'grey') {
        this.$confirm(`确定灰度发布任务 ${row.value.taskId} 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          greyPublish(row.value).then(result => {
            this.crud.notify(result, 'success')
            // 刷新当前页面，保留搜索条件
            this.crud.refresh()
          })
        })
      } else if (row.action === 'timing') {
        this.timingForm.taskId = row.value.taskId
        this.dialogTimingVisible = true
      }
    },
    timingPublish() {
      console.info(this.timingForm)
      timingPublish(this.timingForm).then(result => {
        this.crud.notify(result, 'success')
        this.dialogTimingVisible = false
        // 刷新当前页面，保留搜索条件
        this.crud.refresh()
      })
    },
    handleChange() {
      let expression = this.formulaeMap[this.selectedFormulae.replace('_redio', '')]
      expression = expression.replace(/param_a/g, this.formulaeParams.param_a)
      expression = expression.replace(/param_b/g, this.formulaeParams.param_b)
      expression = expression.replace(/param_c/g, this.formulaeParams.param_c)
      expression = expression.replace(/param_d/g, this.formulaeParams.param_d)
      expression = expression.replace(/param_e/g, this.formulaeParams.param_e)
      if (expression.length === 0) {
        expression = this.formulaeParams.param_a
      }
      form.rewardAmount = expression
      this.form.rewardAmount = form.rewardAmount
      this.computeFormula = form.rewardAmount
    },
    cropUploadSuccess(response, file, fileList) {
      console.info(response)
      this.form.icon = file
      this.dialogVisible = true
    },
    toggleShow() {
      this.show = !this.show
    },
    closeDrawer() {
      this.drawer = false
      // 清空抽屉数据
      this.tableData = []
    },
    evaluateFormula(key) {
      // 获取 formulaeMap 中的表达式
      return this.formulaeMap[key]
    },
    exportJson() {
      this.loading = false
      exportJson(this.taskId).then(result => {
        this.innerDrawer = true
        this.innerDrawerDate = result
        this.loading = true
      })
    },
    exportAllJson() {
      const taskIds = []
      this.crud.selections.forEach(function(element) {
        taskIds.push(element.taskId)
      })
      if (taskIds.length === 0) {
        this.$message({
          message: '请选择要导出的任务',
          type: 'warning'
        })
        return
      }
      this.loading = false
      console.info(taskIds)
      exportAllJson(taskIds).then(result => {
        this.innerAllDrawer = true
        this.innerAllDrawerDate = result
        this.loading = true
      })
    },
    importJSON() {
      this.loading = false
      console.info(this.importJSONDate)
      importJSON(this.importJSONDate).then(result => {
        this.dialogFormVisible = false
        this.crud.resetQuery()
        this.loading = true
        this.importJSONDate = null
      })
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters([
      'taskIconUploadApi'
    ])
  }
}
</script>

<style scoped>
.custom-table .el-table {
  border: 1px solid #e0e0e0; /* 设置表格整体边框 */
  border-spacing: 0; /* 控制单元格之间的间距 */
}

.el-table th .cell {
  font-weight: bold; /* 加粗列头文本 */
}

.custom-table .el-table th,
.custom-table .el-table td {
  padding: 10px 10px; /* 调整单元格内边距 */
  border: 1px solid #e0e0e0; /* 设置单元格边框 */
}

.custom-table .el-table th {
  background-color: #f0f0f0; /* 设置表头背景色 */
  font-weight: bold; /* 表头文字加粗 */
}

.el-row .el-col {
  width: 30%;
  margin-left:20px;
}

</style>
