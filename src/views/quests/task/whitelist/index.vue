、<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">SaaS ID</label>
        <el-input v-model="query.saasId" clearable placeholder="SaaS ID" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="SaaS ID" prop="saasId">
            <el-select v-model="form.saasId" filterable placeholder="请选择" style="width: 370px;">
              <el-option
                v-for="item in dict.saas_id"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用户id" prop="flag">
            <el-input v-model="form.flag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="saasId" label="SaaS ID">
          <template slot-scope="scope">
            {{ dict.label.saas_id[scope.row.saasId] }}
          </template>
        </el-table-column>
        <el-table-column prop="flag" label="用户id" />
        <el-table-column prop="remark" label="备注" />
        <el-table-column v-if="checkPer(['admin','taskWhitelist:edit','taskWhitelist:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTaskWhitelist from '@/api/quests/task/whitelist/taskWhitelist'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, saasId: null, flag: null, remark: null }
export default {
  name: 'TaskWhitelist',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['saas_id'],
  cruds() {
    return CRUD({ title: 'TaskWhite', url: 'api/taskWhitelist', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTaskWhitelist }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'taskWhitelist:add'],
        edit: ['admin', 'taskWhitelist:edit'],
        del: ['admin', 'taskWhitelist:del']
      },
      rules: {
        saasId: [
          { required: true, message: 'SaaS ID不能为空', trigger: 'blur' }
        ],
        flag: [
          { required: true, message: '用户id不能为空', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '备注不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'saasId', display_name: 'SaaS ID' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
