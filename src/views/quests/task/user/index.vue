<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle" style="margin: 1.2%">

        <!-- 搜索 -->
        <label class="el-form-item-label">saasId</label>
        <el-select v-model="query.saasId" clearable size="small" placeholder="SaasId" class="filter-item" style="width: 6%">
          <el-option v-for="item in dict.saas_id" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">twitter handle</label>
        <el-input v-model="query.twitterHandle" clearable placeholder="twitter handle" style="width: 8%;" class="filter-item" />
        <label class="el-form-item-label">discord handle</label>
        <el-input v-model="query.discordHandle" clearable placeholder="discord handle" style="width: 9%;" class="filter-item" />

        <label class="el-form-item-label" style="width: 6%;">用户足迹起始</label>
        <el-date-picker
          v-model="query.dateRange"
          type="datetimerange"
          range-separator=""
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          size="small"
          style="margin-right: 10px; height: 25px; width:19%"
        />

        <el-button size="mini" type="primary" class="el-button filter-item el-button--success el-button--mini" @click="handleSearch()">
          <i class="el-icon-search" />搜索</el-button>
      </div>

      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->

      <!--表格渲染-->
      <div class="block" style="margin: 1.5%">
        <el-dialog title="请选择匹配到的用户" :visible.sync="userListDialogVisable">
          <el-table :data="gridData">
            <el-table-column property="saasId" label="SaasId" width="150" />
            <el-table-column property="uid" label="UID" width="200" />
            <el-table-column property="cid" label="CID" />
            <el-table-column property="twitterName" label="X handle" />
            <el-table-column property="discordName" label="DC handle" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="showUserDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>

      </div>

      <!--用户信息-->
      <div class="block" style="margin: 1.5%">
        <el-descriptions class="margin-top" title="基本信息" :column="3" border>
          <el-descriptions-item>
            <template slot="label">
              SaasId
            </template>
            {{ taskUserData.saasId }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              UID
            </template>
            {{ taskUserData.uid }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              CID
            </template>
            {{ taskUserData.cid }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              X handle
            </template>
            {{ taskUserData.twitterName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              DC handle
            </template>
            {{ taskUserData.discordName }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-row>
        <el-col :span="7" style="margin: 1%">
          <div class="block">
            <label class="el-form-item-label">积分流水 (当前总积分: {{ pointLedgerList.available || '--' }})</label>
            <label class="el-form-item-label">可指定业务类型</label>
            <el-select v-model="query.eventCode" clearable size="small" placeholder="business" class="filter-item" style="width: 100px">
              <el-option v-for="item in dict.event_code" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
            <el-timeline style="margin-top: 2%">
              <el-timeline-item
                v-for="(pointLedger, index) in pointLedgers"
                :key="index"
                :icon="pointLedger.icon"
                :type="index === 0 ? 'primary' : pointLedger.type"
                :color="pointLedger.color"
                :timestamp="formatTimestamp(pointLedger.created)"
              >
                <span v-if="(pointLedger.descI18n || pointLedger.businessType) && pointLedger.amount">
                  complete: {{ pointLedger.descI18n || pointLedger.businessType }}, get points: {{ pointLedger.amount }}</span>
              </el-timeline-item>
            </el-timeline>
            <!--分页组件-->
            <template>
              <el-pagination
                :page-size.sync="page.size"
                :total="page.total"
                :current-page.sync="page.page"
                style="margin-top: 8px;"
                layout="total, prev, pager, next, sizes"
                @size-change="handlePageChange"
                @current-change="handlePageChange"
              />
            </template>
          </div>
        </el-col>
        <el-col :span="15" style="margin: 1%">
          <div class="block">
            <div class="block">
              <label class="el-form-item-label" style="margin: 0">用户足迹 (可指定时间范围，默认查询7日内日志数据)</label>
            </div>
            <el-timeline style="margin-top: 1%">
              <el-timeline-item
                v-for="(track, index) in userTrackLists"
                :key="index"
                :type="index === 0 ? 'primary' : track.type"
                :timestamp="track.trackId"
                placement="top"
              >
                <el-card>
                  <p v-for="(data, dataIndex) in track.trackData" :key="dataIndex">{{ data }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-col>
      </el-row>

    </div>
  </div>
</template>

<script>
import crudTaskUser from '@/api/quests/task/user/taskUser'
import { getToken } from '@/utils/auth'
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import { mapGetters } from 'vuex'
import { initData } from '@/api/data'
import { pagination } from '@crud/crud'

const defaultForm = { id: null, saasId: null, twitter_handle: null, discord_handle: null, icon: null }
export default {
  name: 'TaskUser',
  components: { crudOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  dicts: ['saas_id', 'event_code'],
  cruds() {
    return CRUD({ title: 'TaskUserController', url: 'api/taskUser', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTaskUser }, optShow: { all: false }})
  },
  data() {
    return {
      headers: {
        'Authorization': getToken()
      },
      fileList: [],
      taskUserData: {},
      pointLedgerList: {},
      pointLedgers: [],
      userTrackLists: [],
      choosenUserId: null,
      userListDialogVisable: false,
      gridData: [],
      permission: {
        add: ['admin', 'taskUser:add'],
        edit: ['admin', 'taskUser:edit'],
        del: ['admin', 'taskUser:del']
      },
      rules: {
        id: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'taskUserUploadApi'
    ])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return false
    },
    formatTimestamp(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleString()
    },
    handlePageChange(page) {
      this.crud.page.page = page
      this.queryTaskUser()
    },
    handleSearch() {
      this.choosenUserId = null
      this.queryTaskUser()
    },
    queryTaskUser() {
      const params = {
        saasId: this.query.saasId,
        twitterHandle: this.query.twitterHandle,
        discordHandle: this.query.discordHandle,
        eventCode: this.query.eventCode,
        logFromTime: this.query.dateRange ? Math.floor(new Date(this.query.dateRange[0]).getTime() / 1000) : null,
        logEndTime: this.query.dateRange ? Math.floor(new Date(this.query.dateRange[1]).getTime() / 1000) : null,
        page: this.crud.page.page - 1,
        size: this.crud.page.size
      }
      initData('api/taskUser', params).then(data => {
        console.log(this.taskUserData)
        if (this.choosenUserId) {
          const matchedUser = data.content.find(user => user.cid === this.choosenUserId)
          if (matchedUser) {
            this.showUserDetail(matchedUser)
          }
        } else {
          this.userListDialogVisable = true
          this.gridData = data.content
        }
      }).catch(err => {
        console.log(err)
      })
    },
    showUserDetail(row) {
      console.log(row)
      this.choosenUserId = row.cid
      this.taskUserData = row
      this.userListDialogVisable = false
      if (this.taskUserData) {
        this.pointLedgerList = this.taskUserData.pointLedgerList || {}
        this.userTrackLists = this.taskUserData.userTrackLists || []
        if (this.pointLedgerList) {
          this.pointLedgers = this.pointLedgerList.pointLedgers || []
        }
      }
      // console.log(this.pointLedgers)
      // console.log(this.userTrackLists)
      this.crud.page.total = row.pointLedgerList.total
    }
  }
}
</script>

<style scoped>

</style>
