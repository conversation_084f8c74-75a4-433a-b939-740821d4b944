<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">saasId</label>
        <el-input v-model="query.saasId" clearable placeholder="saasId" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">code</label>
        <el-input v-model="query.code" clearable placeholder="code" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="saasId">
            <el-select v-model="form.saasId" filterable placeholder="请选择">
              <el-option
                v-for="item in dict.saas_id"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="code">
            <el-input v-model="form.code" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="icon">
            <el-upload
              v-model="form.icon"
              class="upload-demo"
              :action="taskIconUploadApi"
              list-type="picture"
              :headers="headers"
              :data="{saasId: form.saasId, code: form.code}"
              :on-success="cropUploadSuccess"
              :file-list="[{name: form.code, url: form.icon}]"
            >
              <el-button v-if="checkPer(['admin', 'taskIcon:upload'])" size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="saasId" label="saasId">
          <template slot-scope="scope">
            {{ dict.label.saas_id[scope.row.saasId] }}
          </template>
        </el-table-column>
        <el-table-column prop="code" label="code" />
        <el-table-column prop="icon" label="icon" />
        <el-table-column v-if="checkPer(['admin','taskIcon:edit','taskIcon:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTaskIcon from '@/api/quests/task/icon/taskIcon'
import { getToken } from '@/utils/auth'
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
import { mapGetters } from 'vuex'

const defaultForm = { id: null, saasId: null, code: null, icon: null }
export default {
  name: 'TaskIcon',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['saas_id'],
  cruds() {
    return CRUD({ title: 'TaskIconController', url: 'api/taskIcon', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTaskIcon }})
  },
  data() {
    return {
      headers: {
        'Authorization': getToken()
      },
      fileList: [],
      permission: {
        add: ['admin', 'taskIcon:add'],
        edit: ['admin', 'taskIcon:edit'],
        del: ['admin', 'taskIcon:del']
      },
      rules: {
        id: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'saasId', display_name: 'saasId' },
        { key: 'code', display_name: 'code' }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'taskIconUploadApi'
    ])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    cropUploadSuccess(esponse, file, fileList) {
      this.fileList = fileList
      this.form.icon = esponse
    },
    toggleShow() {
      this.show = !this.show
    }
  }
}
</script>

<style scoped>

</style>
