<template>
  <div class="body">
    <div class="shell">
      <div class="container a-container" :class="{'is-hidden': isHiddenSignUp, 'is-txl': isTxl}">
        <form action="" method="" class="form">
          <h2 class="form_title title">创建账号</h2>
          <span class="form_span">选择注册方式活电子邮箱注册</span>
          <input type="text" class="form_input" placeholder="Name">
          <input type="text" class="form_input" placeholder="Email">
          <input type="text" class="form_input" placeholder="Password">
          <button class="form_button button submit" @click.prevent>SIGN UP</button>
        </form>
      </div>
      <div class="container b-container" :class="{'is-hidden': isHiddenSignIn, 'is-txl': isTxl, 'is-z': isZ}">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" label-position="left" label-width="0px" class="login-form">
          <span class="form_span">选择登录方式活电子邮箱登录</span>
          <el-form-item prop="username">
            <input v-model="loginForm.username" type="text" class="form_input" placeholder="Email">
          </el-form-item>
          <el-form-item prop="password">
            <input v-model="loginForm.password" type="password" class="form_input" placeholder="Password">
          </el-form-item>
          <el-button :loading="loading" class="form_button button submit" @click.native.prevent="handleLogin">
            <span v-if="!loading">SIGN IN</span>
            <span v-else>LOADING</span>
          </el-button>
        </el-form>
      </div>
      <div class="switch" :class="{'is-gx': isGx, 'is-txr': isTxr}">
        <div class="switch_circle" :class="{'is-txr': isTxr}" />
        <div class="switch_circle switch_circle-t" :class="{'is-txr': isTxr}" />
        <div class="switch_container" :class="{'is-hidden': isHiddenSignUp}">
          <h2 class="switch_title title" style="letter-spacing: 0;">Welcome Back！</h2>
          <p class="switch_description description">已经有账号了嘛，去登入账号来进入奇妙世界吧！！！</p>
          <button class="switch_button button" @click="changeForm">SIGN IN</button>
        </div>

        <div class="switch_container" :class="{'is-hidden': isHiddenSignIn}">
          <h2 class="switch_title title" style="letter-spacing: 0;">Hello Friend！</h2>
          <p class="switch_description description">去注册一个账号，让我们踏入奇妙的旅途！</p>
          <button class="switch_button button" @click="changeForm">SIGN UP</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from '@/api/login'
import Background from '@/assets/images/background.jpeg'
import Config from '@/settings'
import { encrypt } from '@/utils/rsaEncrypt'
import Cookies from 'js-cookie'
import qs from 'qs'

export default {
  name: 'Login',
  data() {
    return {
      isHiddenSignIn: false,
      isHiddenSignUp: true,
      isGx: true,
      isTxr: true,
      isTxl: true,
      isZ: true,
      Background: Background,
      codeUrl: '',
      cookiePass: '',
      loginForm: {
        username: 'admin',
        password: '',
        rememberMe: true
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        password: [{ required: true, trigger: 'blur', message: '密码不能为空' }]
      },
      loading: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const data = route.query
        if (data && data.redirect) {
          this.redirect = data.redirect
          delete data.redirect
          if (JSON.stringify(data) !== '{}') {
            this.redirect = this.redirect + '&' + qs.stringify(data, { indices: false })
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // 获取用户名密码等Cookie
    this.getCookie()
    // token 过期提示
    this.point()
  },
  methods: {
    changeForm() {
      this.isHiddenSignIn = !this.isHiddenSignIn
      this.isHiddenSignUp = !this.isHiddenSignUp
      this.isGx = true
      setTimeout(function() {
        this.isGx = false
      }, 1500)
      this.isTxr = !this.isTxr
      this.isTxl = !this.isTxl
      this.isZ = !this.isZ
    },
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = res.img
        this.loginForm.uuid = res.uuid
      })
    },
    getCookie() {
      const username = Cookies.get('username')
      let password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      // 保存cookie里面的加密后的密码
      this.cookiePass = password === undefined ? '' : password
      password = password === undefined ? this.loginForm.password : password
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        code: ''
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        const user = {
          username: this.loginForm.username,
          password: this.loginForm.password
        }
        if (user.password !== this.cookiePass) {
          user.password = encrypt(user.password)
        }

        if (valid) {
          this.loading = true
          Cookies.set('username', user.username, { expires: Config.passCookieExpires })
          Cookies.set('password', user.password, { expires: Config.passCookieExpires })
          Cookies.set('rememberMe', true, { expires: Config.passCookieExpires })

          this.$store.dispatch('Login', user).then(() => {
            this.loading = false
            this.$router.push({ path: this.redirect || 'task/manager/index' })
          }).catch(() => {
            this.loading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    point() {
      const point = Cookies.get('point') !== undefined
      if (point) {
        this.$notify({
          title: '提示',
          message: '当前登录状态已过期，请重新登录！',
          type: 'warning',
          duration: 5000
        })
        Cookies.remove('point')
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">

  * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .body {
            width: 100%;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            background-color: #ecf0f3;
            color: #a0a5a8;
        }

        .shell {
            position: relative;
            width: 1000px;
            min-width: 1000px;
            min-height: 600px;
            height: 600px;
            padding: 25px;
            background-color: #ecf0f3;
            box-shadow: 10px 10px 10px #d1d9e6, -10px -10px 10px #f9f9f9;
            border-radius: 12px;
            overflow: hidden;
        }

        /* 设置响应式 */
        @media (max-width: 1200px) {
            .shell {
                transform: scale(0.7);
            }
        }

        @media (max-width: 1000px) {
            .shell {
                transform: scale(0.6);
            }
        }

        @media (max-width: 800px) {
            .shell {
                transform: scale(0.5);
            }
        }

        @media (max-width: 600px) {
            .shell {
                transform: scale(0.4);
            }
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            width: 600px;
            height: 100%;
            padding: 25px;
            background-color: #ecf0f3;
            transition: 1.25s;
        }

        .form {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            width: 100%;
            height: 100%;
        }

        .iconfont {
            margin: 0 5px;
            border: rgba(0, 0, 0, 0.5) 2px solid;
            border-radius: 50%;
            font-size: 25px;
            padding: 3px;
            opacity: 0.5;
            transition: 0.1s;
        }

        .iconfont:hover {
            opacity: 1;
            transition: 0.15s;
            cursor: pointer;
        }

        .form_input {
            width: 350px;
            height: 40px;
            margin: 4px 0;
            padding-left: 25px;
            font-size: 13px;
            letter-spacing: 0.15px;
            border: none;
            outline: none;
            background-color: #ecf0f3;
            transition: 0.25s ease;
            border-radius: 8px;
            box-shadow: inset 2px 2px 4px #d1d9e6, inset -2px -2px 4px #f9f9f9;
        }

        .form_input:focus {
            box-shadow: inset 4px 4px 4px #d1d9e6, inset -4px -4px 4px #f9f9f9;
        }

        .form_span {
            margin-top: 30px;
            margin-bottom: 12px;
        }

        .form_link {
            color: #181818;
            font-size: 15px;
            margin-top: 25px;
            border-bottom: 1px solid #a0a5a8;
            line-height: 2;
        }

        .title {
            font-size: 34px;
            font-weight: 700;
            line-height: 3;
            color: #181818;
            letter-spacing: 10px;
        }

        .description {
            font-size: 14px;
            letter-spacing: 0.25px;
            text-align: center;
            line-height: 1.6;
        }

        .button {
            width: 180px;
            height: 50px;
            border-radius: 25px;
            margin-top: 50px;
            font-weight: 700;
            font-size: 14px;
            letter-spacing: 1.15px;
            background-color: #4B70E2;
            color: #f9f9f9;
            box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #f9f9f9;
            border: none;
            outline: none;
        }

        .a-container {
            z-index: 100;
            left: calc(100% - 600px);
        }

        .b-container {
            left: calc(100% - 600px);
            z-index: 0;
        }

        .switch {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 400px;
            padding: 50px;
            z-index: 200;
            transition: 1.25s;
            background-color: #ecf0f3;
            overflow: hidden;
            box-shadow: 4px 4px 10px #d1d9e6, -4px -4px 10px #d1d9e6;
        }

        .switch_circle {
            position: absolute;
            width: 500px;
            height: 500px;
            border-radius: 50%;
            background-color: #ecf0f3;
            box-shadow: inset 8px 8px 12px #b8bec7, inset -8px -8px 12px #fff;
            bottom: -60%;
            left: -60%;
            transition: 1.25s;
        }

        .switch_circle-t {
            top: -30%;
            left: 60%;
            width: 300px;
            height: 300px;
        }

        .switch_container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            position: absolute;
            width: 400px;
            padding: 50px 55px;
            transition: 1.25s;
        }

        .switch_button {
            cursor: pointer;
        }

        .switch_button:hover,
        .submit:hover {
            box-shadow: 6px 6px 10px #d1d9e6, -6px -6px 10px #f9f9f9;
            transform: scale(0.985);
            transition: 0.25s;
        }

        .switch_button:active,
        .switch_button:focus {
            box-shadow: 2px 2px 6px #d1d9e6, -2px -2px 6px #f9f9f9;
            transform: scale(0.97);
            transition: 0.25s;
        }

        .is-txr {
            left: calc(100% - 400px);
            transition: 1.25s;
            transform-origin: left;
        }

        .is-txl {
            left: 0;
            transition: 1.25s;
            transform-origin: right;
        }

        .is-z {
            z-index: 200;
            transition: 1.25s;
        }

        .is-hidden {
            visibility: hidden;
            opacity: 0;
            position: absolute;
            transition: 1.25s;
        }

        .is-gx {
            animation: is-gx 1.25s;
        }

        @keyframes is-gx {

            0%,
            10%,
            100% {
                width: 400px;
            }

            30%,
            50% {
                width: 500px;
            }
        }
</style>
